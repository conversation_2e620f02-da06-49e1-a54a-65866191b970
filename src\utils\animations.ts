import { interpolate, spring } from 'remotion';

// 文字逐字显示动画
export const getTextRevealProgress = (
  frame: number,
  startFrame: number,
  durationInFrames: number,
  textLength: number,
  index: number
): number => {
  const progress = interpolate(
    frame,
    [startFrame + index * 3, startFrame + index * 3 + 15],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );
  return progress;
};

// 弹性进入动画
export const getSpringAnimation = (
  frame: number,
  startFrame: number,
  fps: number
): number => {
  return spring({
    frame: frame - startFrame,
    fps,
    config: {
      damping: 10,
      stiffness: 100,
      mass: 0.5,
    },
  });
};

// 淡入淡出动画
export const getFadeAnimation = (
  frame: number,
  startFrame: number,
  durationInFrames: number,
  fadeInDuration: number = 30,
  fadeOutDuration: number = 30
): number => {
  const endFrame = startFrame + durationInFrames;
  
  if (frame < startFrame) return 0;
  if (frame > endFrame) return 0;
  
  // 淡入
  if (frame < startFrame + fadeInDuration) {
    return interpolate(frame, [startFrame, startFrame + fadeInDuration], [0, 1]);
  }
  
  // 淡出
  if (frame > endFrame - fadeOutDuration) {
    return interpolate(frame, [endFrame - fadeOutDuration, endFrame], [1, 0]);
  }
  
  return 1;
};

// 滑动进入动画
export const getSlideAnimation = (
  frame: number,
  startFrame: number,
  durationInFrames: number,
  direction: 'left' | 'right' | 'up' | 'down' = 'up'
): { x: number; y: number; opacity: number } => {
  const progress = interpolate(
    frame,
    [startFrame, startFrame + durationInFrames],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const distance = 50;
  let x = 0;
  let y = 0;

  switch (direction) {
    case 'left':
      x = interpolate(progress, [0, 1], [-distance, 0]);
      break;
    case 'right':
      x = interpolate(progress, [0, 1], [distance, 0]);
      break;
    case 'up':
      y = interpolate(progress, [0, 1], [distance, 0]);
      break;
    case 'down':
      y = interpolate(progress, [0, 1], [-distance, 0]);
      break;
  }

  const opacity = interpolate(progress, [0, 1], [0, 1]);

  return { x, y, opacity };
};
