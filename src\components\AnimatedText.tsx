import React from 'react';
import { useCurrentFrame, useVideoConfig } from 'remotion';
import { getTextRevealProgress, getFadeAnimation } from '../utils/animations';

interface AnimatedTextProps {
  text: string;
  startFrame: number;
  className?: string;
  animationType?: 'reveal' | 'fade' | 'typewriter';
  duration?: number;
  delay?: number;
}

export const AnimatedText: React.FC<AnimatedTextProps> = ({
  text,
  startFrame,
  className = '',
  animationType = 'reveal',
  duration = 60,
  delay = 0,
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const actualStartFrame = startFrame + delay;

  if (animationType === 'fade') {
    const opacity = getFadeAnimation(frame, actualStartFrame, duration);
    return (
      <div 
        className={className}
        style={{ opacity }}
      >
        {text}
      </div>
    );
  }

  if (animationType === 'typewriter') {
    const progress = Math.max(0, Math.min(1, (frame - actualStartFrame) / duration));
    const visibleLength = Math.floor(progress * text.length);
    const visibleText = text.slice(0, visibleLength);
    
    return (
      <div className={className}>
        {visibleText}
        {progress < 1 && <span className="animate-pulse">|</span>}
      </div>
    );
  }

  // Default: reveal animation
  return (
    <div className={className}>
      {text.split('').map((char, index) => {
        const progress = getTextRevealProgress(
          frame,
          actualStartFrame,
          duration,
          text.length,
          index
        );
        
        return (
          <span
            key={index}
            style={{
              opacity: progress,
              transform: `translateY(${(1 - progress) * 20}px)`,
              display: char === ' ' ? 'inline' : 'inline-block',
            }}
          >
            {char === ' ' ? '\u00A0' : char}
          </span>
        );
      })}
    </div>
  );
};

export default AnimatedText;
